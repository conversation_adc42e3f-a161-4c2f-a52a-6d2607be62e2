/**
 * @file SERVERINFO.JS
 *
 * @version 1.0.0
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Admin command to display comprehensive server information.
 * Shows server statistics, member counts, channel counts, roles, and other details.
 * Available to HR and admin users for server management purposes.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------ IMPORTS
import { EmbedBuilder, MessageFlags, SlashCommandBuilder } from 'discord.js';

import { botConfig } from '../../config/config.js';
import { CommandMiddleware } from '../../middleware/commandAuthentication.js';
import { CommandHandler } from '../../middleware/commandHandler.js';
import * as cooldown from '../../utils/cooldown.js';
import { handleError } from '../../utils/errorHandler.js';
import { checkRateLimit, getRateLimitStatus } from '../../utils/rateLimit.js';
import { safeReply } from '../../utils/replyHelpers.js';

// ------------------------ TYPE SAFETY ANNOTATIONS ------------------------ //
/**
 * @typedef {import('discord.js').ChatInputCommandInteraction} ChatInputCommandInteraction
 * @typedef {import('discord.js').Guild} Guild
 * @typedef {import('discord.js').GuildMember} GuildMember
 */

// ------------ UTILITY FUNCTIONS
/**
 * Format a number with commas for better readability.
 * @param {number} num - Number to format
 * @returns {string} - Formatted number string
 */
function formatNumber(num) {
  return num.toLocaleString();
}

/**
 * Get verification level name.
 * @param {number} level - Verification level number
 * @returns {string} - Human-readable verification level
 */
function getVerificationLevel(level) {
  const levels = {
    0: 'None',
    1: 'Low',
    2: 'Medium',
    3: 'High',
    4: 'Very High',
  };
  return levels[level] || 'Unknown';
}

/**
 * Get explicit content filter level name.
 * @param {number} level - Filter level number
 * @returns {string} - Human-readable filter level
 */
function getExplicitContentFilter(level) {
  const levels = {
    0: 'Disabled',
    1: 'Members without roles',
    2: 'All members',
  };
  return levels[level] || 'Unknown';
}

/**
 * Get MFA level name.
 * @param {number} level - MFA level number
 * @returns {string} - Human-readable MFA level
 */
function getMfaLevel(level) {
  const levels = {
    0: 'None',
    1: 'Elevated',
  };
  return levels[level] || 'Unknown';
}

/**
 * Calculate server age in a human-readable format.
 * @param {Date} createdAt - Server creation date
 * @returns {string} - Formatted age string
 */
function getServerAge(createdAt) {
  const now = new Date();
  const diffMs = now.getTime() - createdAt.getTime();
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  const diffYears = Math.floor(diffDays / 365);
  const remainingDays = diffDays % 365;

  if (diffYears > 0) {
    return `${diffYears} year${diffYears > 1 ? 's' : ''}, ${remainingDays} day${remainingDays !== 1 ? 's' : ''}`;
  }
  return `${diffDays} day${diffDays !== 1 ? 's' : ''}`;
}

// ------------ SLASH COMMAND BUILDER
export const data = new SlashCommandBuilder()
  .setName('serverinfo')
  .setDescription('Display comprehensive server information and statistics 📊');

/**
 * Execute the serverinfo command.
 * @param {ChatInputCommandInteraction} interaction - The command interaction.
 * @returns {Promise<void>}
 */
async function _execute(interaction) {
  try {
    // Rate limit check
    const rateLimitContext = {
      userId: interaction.user.id,
      channelId: interaction.channelId,
      guildId: interaction.guildId ?? undefined,
      commandName: 'serverinfo',
    };
    const rate = checkRateLimit(rateLimitContext);
    if (rate.limited) {
      const status = getRateLimitStatus(rateLimitContext);
      return await safeReply(interaction, {
        content: `📊 Server info command is rate limited! Try again in ${Math.ceil(rate.resetTime / 1000)} seconds. You have ${status.remaining} requests left.`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Cooldown check
    const cooldownKey = `serverinfo:${interaction.user.id}`;
    const serverinfoCooldown = cooldown.getCooldownMs('serverinfo');
    if (!cooldown.check(cooldownKey, serverinfoCooldown)) {
      return await safeReply(interaction, {
        content: `📊 Please wait ${Math.ceil(cooldown.getRemaining(cooldownKey, serverinfoCooldown) / 1000)}s before using serverinfo again.`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Validate we're in a guild
    if (!interaction.guild) {
      return await safeReply(interaction, {
        content: '📊 This command can only be used in a server!',
        flags: [MessageFlags.Ephemeral],
      });
    }

    const guild = interaction.guild;

    // Fetch additional guild data
    await guild.members.fetch();
    await guild.channels.fetch();
    await guild.roles.fetch();

    // Calculate member statistics
    const totalMembers = guild.memberCount;
    const onlineMembers = guild.members.cache.filter(member => 
      member.presence?.status === 'online' || 
      member.presence?.status === 'idle' || 
      member.presence?.status === 'dnd'
    ).size;
    const botMembers = guild.members.cache.filter(member => member.user.bot).size;
    const humanMembers = totalMembers - botMembers;

    // Calculate channel statistics
    const textChannels = guild.channels.cache.filter(channel => channel.isTextBased() && !channel.isThread()).size;
    const voiceChannels = guild.channels.cache.filter(channel => channel.isVoiceBased()).size;
    const categories = guild.channels.cache.filter(channel => channel.type === 4).size; // CategoryChannel
    const totalChannels = guild.channels.cache.size;

    // Role statistics
    const totalRoles = guild.roles.cache.size;
    const managedRoles = guild.roles.cache.filter(role => role.managed).size;

    // Server features
    const features = guild.features.length > 0 ? guild.features.join(', ') : 'None';

    // Create main embed
    const embed = new EmbedBuilder()
      .setColor(parseInt(botConfig.colors.primary.replace(/^#/, ''), 16))
      .setTitle(`📊 ${guild.name} - Server Information`)
      .setThumbnail(guild.iconURL({ size: 256 }) || null)
      .addFields(
        {
          name: '🆔 Server ID',
          value: guild.id,
          inline: true,
        },
        {
          name: '👑 Owner',
          value: guild.ownerId ? `<@${guild.ownerId}>` : 'Unknown',
          inline: true,
        },
        {
          name: '📅 Created',
          value: `<t:${Math.floor(guild.createdTimestamp / 1000)}:F>`,
          inline: true,
        },
        {
          name: '⏰ Server Age',
          value: getServerAge(guild.createdAt),
          inline: true,
        },
        {
          name: '🌍 Region',
          value: guild.preferredLocale || 'Unknown',
          inline: true,
        },
        {
          name: '🔒 Verification Level',
          value: getVerificationLevel(guild.verificationLevel),
          inline: true,
        }
      );

    // Add member statistics
    embed.addFields(
      {
        name: '👥 Members',
        value: [
          `**Total:** ${formatNumber(totalMembers)}`,
          `**Humans:** ${formatNumber(humanMembers)}`,
          `**Bots:** ${formatNumber(botMembers)}`,
          `**Online:** ${formatNumber(onlineMembers)}`,
        ].join('\n'),
        inline: true,
      },
      {
        name: '📺 Channels',
        value: [
          `**Total:** ${formatNumber(totalChannels)}`,
          `**Text:** ${formatNumber(textChannels)}`,
          `**Voice:** ${formatNumber(voiceChannels)}`,
          `**Categories:** ${formatNumber(categories)}`,
        ].join('\n'),
        inline: true,
      },
      {
        name: '🎭 Roles',
        value: [
          `**Total:** ${formatNumber(totalRoles)}`,
          `**Managed:** ${formatNumber(managedRoles)}`,
          `**Custom:** ${formatNumber(totalRoles - managedRoles)}`,
        ].join('\n'),
        inline: true,
      }
    );

    // Add security and features
    embed.addFields(
      {
        name: '🛡️ Security',
        value: [
          `**MFA Level:** ${getMfaLevel(guild.mfaLevel)}`,
          `**Content Filter:** ${getExplicitContentFilter(guild.explicitContentFilter)}`,
        ].join('\n'),
        inline: true,
      },
      {
        name: '✨ Features',
        value: features.length > 100 ? features.substring(0, 97) + '...' : features,
        inline: false,
      }
    );

    // Add server banner if available
    if (guild.bannerURL()) {
      embed.setImage(guild.bannerURL({ size: 1024 }));
    }

    embed
      .setFooter({ 
        text: `Dynamic Innovative Studio • ${botConfig.version}`,
        iconURL: interaction.client.user?.displayAvatarURL(),
      })
      .setTimestamp();

    await safeReply(interaction, { embeds: [embed] });

    // Set cooldown after successful execution
    cooldown.set(cooldownKey);

  } catch (error) {
    await handleError(
      error instanceof Error ? error : new Error(String(error)),
      { command: 'serverinfo' },
      undefined,
      {
        critical: false,
      }
    );
    await safeReply(interaction, {
      content: '📊 Failed to retrieve server information. Please try again later.',
      flags: [MessageFlags.Ephemeral],
    });
  }
}

// ------------ EXPORT
export default {
  data,
  execute: CommandHandler.standard(
    'serverinfo',
    CommandMiddleware.hrAndAdmin(_execute)
  ),
};
