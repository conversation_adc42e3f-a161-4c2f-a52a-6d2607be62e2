/**
 * @file PURGE.JS
 *
 * @version 1.0.0
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Admin command to bulk delete messages from a channel.
 * Allows HR and admin users to clean up channels by removing multiple messages at once.
 * Includes safety limits and proper permission checks.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check
/* eslint-disable max-statements */

// ------------ IMPORTS
import {
  EmbedBuilder,
  MessageFlags,
  PermissionFlagsBits,
  SlashCommandBuilder,
} from 'discord.js';

import { botConfig } from '../../config/config.js';
import { CommandMiddleware } from '../../middleware/commandAuthentication.js';
import { CommandHandler } from '../../middleware/commandHandler.js';
import * as cooldown from '../../utils/cooldown.js';
import { handleError } from '../../utils/errorHandler.js';
import logger from '../../utils/logger.js';
import { checkRateLimit, getRateLimitStatus } from '../../utils/rateLimit.js';
import { safeReply } from '../../utils/replyHelpers.js';

// ------------------------ TYPE SAFETY ANNOTATIONS ------------------------ //
/**
 * @typedef {import('discord.js').ChatInputCommandInteraction} ChatInputCommandInteraction
 * @typedef {import('discord.js').TextChannel} TextChannel
 * @typedef {import('discord.js').Collection} Collection
 * @typedef {import('discord.js').Message} Message
 */

// ------------ PURGE UTILITIES
/**
 * Calculate how many messages can be bulk deleted (Discord limit: 14 days old).
 * @param {Collection<string, Message>} messages - Collection of messages
 * @returns {number} - Number of messages that can be bulk deleted
 */
function getBulkDeletableCount(messages) {
  const twoWeeksAgo = Date.now() - 14 * 24 * 60 * 60 * 1000;
  return messages.filter(msg => msg.createdTimestamp > twoWeeksAgo).size;
}

/**
 * Format duration in a human-readable way.
 * @param {number} ms - Duration in milliseconds
 * @returns {string} - Formatted duration
 */
function formatDuration(ms) {
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  if (days > 0) return `${days}d ${hours % 24}h`;
  if (hours > 0) return `${hours}h ${minutes % 60}m`;
  if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
  return `${seconds}s`;
}

// ------------ SLASH COMMAND BUILDER
export const data = new SlashCommandBuilder()
  .setName('purge')
  .setDescription('Bulk delete messages from the current channel 🗑️')
  .addIntegerOption(option =>
    option
      .setName('amount')
      .setDescription('Number of messages to delete (1-100)')
      .setRequired(true)
      .setMinValue(1)
      .setMaxValue(100)
  )
  .addUserOption(option =>
    option
      .setName('user')
      .setDescription('Only delete messages from this user')
      .setRequired(false)
  )
  .addStringOption(option =>
    option
      .setName('reason')
      .setDescription('Reason for the purge')
      .setRequired(false)
      .setMaxLength(500)
  );

/**
 * Execute the purge command.
 * @param {ChatInputCommandInteraction} interaction - The command interaction.
 * @returns {Promise<void>}
 */
async function _execute(interaction) {
  try {
    // Rate limit check
    const rateLimitContext = {
      userId: interaction.user.id,
      channelId: interaction.channelId,
      guildId: interaction.guildId ?? undefined,
      commandName: 'purge',
    };
    const rate = checkRateLimit(rateLimitContext);
    if (rate.limited) {
      const status = getRateLimitStatus(rateLimitContext);
      return await safeReply(interaction, {
        content: `🗑️ Purge command is rate limited! Try again in ${Math.ceil(rate.resetTime / 1000)} seconds. You have ${status.remaining} requests left.`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Cooldown check
    const cooldownKey = `purge:${interaction.user.id}`;
    const purgeCooldown = cooldown.getCooldownMs('purge');
    if (!cooldown.check(cooldownKey, purgeCooldown)) {
      return await safeReply(interaction, {
        content: `🗑️ Please wait ${Math.ceil(cooldown.getRemaining(cooldownKey, purgeCooldown) / 1000)}s before using purge again.`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Validate we're in a guild
    if (!interaction.guild || !interaction.channel) {
      return await safeReply(interaction, {
        content: '🗑️ This command can only be used in a server channel!',
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Get the channel and ensure it's a text channel
    const channel = interaction.channel;
    if (!channel.isTextBased() || channel.isDMBased()) {
      return await safeReply(interaction, {
        content: '🗑️ This command can only be used in text channels!',
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Get options
    const amount = interaction.options.getInteger('amount', true);
    const targetUser = interaction.options.getUser('user');
    const reason =
      interaction.options.getString('reason') ?? 'No reason provided';

    // Check bot permissions
    const botMember = interaction.guild.members.me;
    if (
      !botMember ||
      !channel
        .permissionsFor(botMember)
        ?.has(PermissionFlagsBits.ManageMessages)
    ) {
      return await safeReply(interaction, {
        content:
          "🗑️ I don't have permission to manage messages in this channel!",
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Defer reply since this might take a moment
    await interaction.deferReply({ flags: [MessageFlags.Ephemeral] });

    // Fetch messages
    const fetchLimit = Math.min(amount * 2, 100); // Fetch more in case we need to filter
    const messages = await channel.messages.fetch({ limit: fetchLimit });

    // Filter messages if user is specified
    let messagesToDelete = messages;
    if (targetUser) {
      messagesToDelete = messages.filter(
        msg => msg.author.id === targetUser.id
      );
    }

    // Limit to the requested amount
    const finalMessages = messagesToDelete.first(amount);

    if (finalMessages.length === 0) {
      return await safeReply(interaction, {
        content: targetUser
          ? `🗑️ No messages found from ${targetUser.toString()} in the recent history.`
          : '🗑️ No messages found to delete.',
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Check how many can be bulk deleted
    const bulkDeletableCount = getBulkDeletableCount(finalMessages);
    const oldMessagesCount = finalMessages.length - bulkDeletableCount;

    let deletedCount = 0;
    const startTime = Date.now();

    // Bulk delete recent messages (less than 14 days old)
    if (bulkDeletableCount > 0) {
      const recentMessages = finalMessages.filter(
        msg => msg.createdTimestamp > Date.now() - 14 * 24 * 60 * 60 * 1000
      );

      if (recentMessages.size > 1) {
        await channel.bulkDelete(recentMessages, true);
        deletedCount += recentMessages.size;
      } else if (recentMessages.size === 1) {
        await recentMessages.first()?.delete();
        deletedCount += 1;
      }
    }

    // Delete old messages individually (if any)
    if (oldMessagesCount > 0) {
      const oldMessages = finalMessages.filter(
        msg => msg.createdTimestamp <= Date.now() - 14 * 24 * 60 * 60 * 1000
      );

      for (const message of oldMessages.values()) {
        try {
          await message.delete();
          deletedCount++;
          // Small delay to avoid rate limits
          await new Promise(resolve => setTimeout(resolve, 100));
        } catch (error) {
          logger.warn(
            'Failed to delete old message:',
            error instanceof Error ? error : new Error(String(error))
          );
        }
      }
    }

    const endTime = Date.now();
    const duration = endTime - startTime;

    // Create success embed
    const embed = new EmbedBuilder()
      .setColor(parseInt(botConfig.colors.success.replace(/^#/, ''), 16))
      .setTitle('🗑️ Messages Purged')
      .setDescription('Messages have been successfully deleted.')
      .addFields(
        {
          name: '📊 Deleted Count',
          value: `${deletedCount}`,
          inline: true,
        },
        {
          name: '📍 Channel',
          value: channel.toString(),
          inline: true,
        },
        {
          name: '👮 Moderator',
          value: interaction.user.toString(),
          inline: true,
        },
        {
          name: '⏱️ Duration',
          value: formatDuration(duration),
          inline: true,
        },
        {
          name: '📝 Reason',
          value: reason,
          inline: false,
        }
      )
      .setFooter({
        text: `Dynamic Innovative Studio • ${botConfig.version}`,
        iconURL: interaction.client.user?.displayAvatarURL(),
      })
      .setTimestamp();

    // Add user filter info if applicable
    if (targetUser) {
      embed.addFields({
        name: '👤 Target User',
        value: targetUser.toString(),
        inline: true,
      });
    }

    // Add warning about old messages if any
    if (oldMessagesCount > 0) {
      embed.addFields({
        name: '⚠️ Note',
        value: `${oldMessagesCount} messages were older than 14 days and deleted individually.`,
        inline: false,
      });
    }

    await safeReply(interaction, { embeds: [embed] });

    // Set cooldown after successful execution
    cooldown.set(cooldownKey);
  } catch (error) {
    await handleError(
      error instanceof Error ? error : new Error(String(error)),
      { command: 'purge' },
      undefined,
      {
        critical: false,
      }
    );
    await safeReply(interaction, {
      content: '🗑️ Failed to purge messages. Please try again later.',
      flags: [MessageFlags.Ephemeral],
    });
  }
}

// ------------ EXPORT
export default {
  data,
  execute: CommandHandler.standard(
    'purge',
    CommandMiddleware.hrAndAdmin(_execute)
  ),
};
