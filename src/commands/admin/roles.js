/**
 * @file ROLES.JS
 *
 * @version 1.0.0
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Admin command to display comprehensive role information and management overview.
 * Shows all server roles with their permissions, member counts, and hierarchy.
 * Available to HR and admin users for role management purposes.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------ IMPORTS
import { EmbedBuilder, MessageFlags, SlashCommandBuilder } from 'discord.js';

import { botConfig } from '../../config/config.js';
import { CommandMiddleware } from '../../middleware/commandAuthentication.js';
import { CommandHandler } from '../../middleware/commandHandler.js';
import * as cooldown from '../../utils/cooldown.js';
import { handleError } from '../../utils/errorHandler.js';
import { checkRateLimit, getRateLimitStatus } from '../../utils/rateLimit.js';
import { safeReply } from '../../utils/replyHelpers.js';

// ------------------------ TYPE SAFETY ANNOTATIONS ------------------------ //
/**
 * @typedef {import('discord.js').ChatInputCommandInteraction} ChatInputCommandInteraction
 * @typedef {import('discord.js').Role} Role
 * @typedef {import('discord.js').Collection} Collection
 */

// ------------ UTILITY FUNCTIONS
/**
 * Format a number with commas for better readability.
 * @param {number} num - Number to format
 * @returns {string} - Formatted number string
 */
function formatNumber(num) {
  return num.toLocaleString();
}

/**
 * Get role color as hex string.
 * @param {Role} role - Discord role
 * @returns {string} - Hex color string
 */
function getRoleColor(role) {
  return role.hexColor === '#000000' ? 'Default' : role.hexColor;
}

/**
 * Get key permissions for a role.
 * @param {Role} role - Discord role
 * @returns {string[]} - Array of key permission names
 */
function getKeyPermissions(role) {
  const keyPerms = [];
  
  if (role.permissions.has('Administrator')) keyPerms.push('Administrator');
  if (role.permissions.has('ManageGuild')) keyPerms.push('Manage Server');
  if (role.permissions.has('ManageRoles')) keyPerms.push('Manage Roles');
  if (role.permissions.has('ManageChannels')) keyPerms.push('Manage Channels');
  if (role.permissions.has('ManageMessages')) keyPerms.push('Manage Messages');
  if (role.permissions.has('KickMembers')) keyPerms.push('Kick Members');
  if (role.permissions.has('BanMembers')) keyPerms.push('Ban Members');
  if (role.permissions.has('ModerateMembers')) keyPerms.push('Timeout Members');
  if (role.permissions.has('MentionEveryone')) keyPerms.push('Mention Everyone');
  
  return keyPerms;
}

/**
 * Truncate text to fit within Discord embed limits.
 * @param {string} text - Text to truncate
 * @param {number} maxLength - Maximum length
 * @returns {string} - Truncated text
 */
function truncateText(text, maxLength) {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength - 3) + '...';
}

// ------------ SLASH COMMAND BUILDER
export const data = new SlashCommandBuilder()
  .setName('roles')
  .setDescription('Display comprehensive role information and management overview 🎭')
  .addStringOption(option =>
    option
      .setName('filter')
      .setDescription('Filter roles by type')
      .setRequired(false)
      .addChoices(
        { name: 'All Roles', value: 'all' },
        { name: 'Staff Roles', value: 'staff' },
        { name: 'Bot Roles', value: 'bot' },
        { name: 'Color Roles', value: 'color' },
        { name: 'Hoisted Roles', value: 'hoisted' }
      )
  );

/**
 * Execute the roles command.
 * @param {ChatInputCommandInteraction} interaction - The command interaction.
 * @returns {Promise<void>}
 */
async function _execute(interaction) {
  try {
    // Rate limit check
    const rateLimitContext = {
      userId: interaction.user.id,
      channelId: interaction.channelId,
      guildId: interaction.guildId ?? undefined,
      commandName: 'roles',
    };
    const rate = checkRateLimit(rateLimitContext);
    if (rate.limited) {
      const status = getRateLimitStatus(rateLimitContext);
      return await safeReply(interaction, {
        content: `🎭 Roles command is rate limited! Try again in ${Math.ceil(rate.resetTime / 1000)} seconds. You have ${status.remaining} requests left.`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Cooldown check
    const cooldownKey = `roles:${interaction.user.id}`;
    const rolesCooldown = cooldown.getCooldownMs('roles');
    if (!cooldown.check(cooldownKey, rolesCooldown)) {
      return await safeReply(interaction, {
        content: `🎭 Please wait ${Math.ceil(cooldown.getRemaining(cooldownKey, rolesCooldown) / 1000)}s before using roles again.`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Validate we're in a guild
    if (!interaction.guild) {
      return await safeReply(interaction, {
        content: '🎭 This command can only be used in a server!',
        flags: [MessageFlags.Ephemeral],
      });
    }

    const guild = interaction.guild;
    const filter = interaction.options.getString('filter') ?? 'all';

    // Fetch roles and members
    await guild.roles.fetch();
    await guild.members.fetch();

    let roles = guild.roles.cache;

    // Apply filter
    switch (filter) {
      case 'staff':
        roles = roles.filter(role => {
          const keyPerms = getKeyPermissions(role);
          return keyPerms.length > 0 && !role.managed;
        });
        break;
      case 'bot':
        roles = roles.filter(role => role.managed);
        break;
      case 'color':
        roles = roles.filter(role => role.hexColor !== '#000000' && !role.managed);
        break;
      case 'hoisted':
        roles = roles.filter(role => role.hoist);
        break;
      case 'all':
      default:
        // Keep all roles
        break;
    }

    // Sort roles by position (highest first)
    const sortedRoles = roles.sort((a, b) => b.position - a.position);

    // Create main embed
    const embed = new EmbedBuilder()
      .setColor(parseInt(botConfig.colors.primary.replace(/^#/, ''), 16))
      .setTitle(`🎭 ${guild.name} - Role Overview`)
      .setDescription(`Showing ${filter === 'all' ? 'all' : filter} roles (${sortedRoles.size} total)`)
      .addFields(
        {
          name: '📊 Statistics',
          value: [
            `**Total Roles:** ${formatNumber(guild.roles.cache.size)}`,
            `**Managed Roles:** ${formatNumber(guild.roles.cache.filter(r => r.managed).size)}`,
            `**Hoisted Roles:** ${formatNumber(guild.roles.cache.filter(r => r.hoist).size)}`,
            `**Color Roles:** ${formatNumber(guild.roles.cache.filter(r => r.hexColor !== '#000000').size)}`,
          ].join('\n'),
          inline: true,
        }
      );

    // Add role list (limited to prevent embed size issues)
    const maxRoles = 20;
    const rolesToShow = sortedRoles.first(maxRoles);
    
    if (rolesToShow.length > 0) {
      const roleList = rolesToShow.map(role => {
        const memberCount = role.members.size;
        const keyPerms = getKeyPermissions(role);
        const permText = keyPerms.length > 0 ? ` • ${keyPerms.slice(0, 2).join(', ')}${keyPerms.length > 2 ? '...' : ''}` : '';
        const managedText = role.managed ? ' 🤖' : '';
        const hoistedText = role.hoist ? ' 📌' : '';
        
        return `${role.toString()} (${memberCount})${permText}${managedText}${hoistedText}`;
      }).join('\n');

      embed.addFields({
        name: `🎭 Roles (Top ${Math.min(maxRoles, sortedRoles.size)})`,
        value: truncateText(roleList, 1024),
        inline: false,
      });

      if (sortedRoles.size > maxRoles) {
        embed.addFields({
          name: '📝 Note',
          value: `Only showing the top ${maxRoles} roles. Use filters to see specific role types.`,
          inline: false,
        });
      }
    } else {
      embed.addFields({
        name: '🎭 Roles',
        value: 'No roles found matching the selected filter.',
        inline: false,
      });
    }

    // Add legend
    embed.addFields({
      name: '📖 Legend',
      value: [
        '🤖 = Managed by bot/integration',
        '📌 = Hoisted (displayed separately)',
        '(number) = Member count',
      ].join('\n'),
      inline: false,
    });

    embed
      .setFooter({ 
        text: `Dynamic Innovative Studio • ${botConfig.version}`,
        iconURL: interaction.client.user?.displayAvatarURL(),
      })
      .setTimestamp();

    await safeReply(interaction, { embeds: [embed] });

    // Set cooldown after successful execution
    cooldown.set(cooldownKey);

  } catch (error) {
    await handleError(
      error instanceof Error ? error : new Error(String(error)),
      { command: 'roles' },
      undefined,
      {
        critical: false,
      }
    );
    await safeReply(interaction, {
      content: '🎭 Failed to retrieve role information. Please try again later.',
      flags: [MessageFlags.Ephemeral],
    });
  }
}

// ------------ EXPORT
export default {
  data,
  execute: CommandHandler.standard(
    'roles',
    CommandMiddleware.hrAndAdmin(_execute)
  ),
};
